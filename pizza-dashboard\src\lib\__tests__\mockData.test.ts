import { mockPizzaOrders, getStatusConfig } from '../mockData'

describe('mockData', () => {
  describe('mockPizzaOrders', () => {
    it('should have the correct number of orders', () => {
      expect(mockPizzaOrders).toHaveLength(28)
    })

    it('should have all required fields for each order', () => {
      mockPizzaOrders.forEach(order => {
        expect(order).toHaveProperty('id')
        expect(order).toHaveProperty('customerName')
        expect(order).toHaveProperty('pizzaType')
        expect(order).toHaveProperty('quantity')
        expect(order).toHaveProperty('orderDate')
        expect(order).toHaveProperty('status')
      })
    })

    it('should have valid order IDs in correct format', () => {
      mockPizzaOrders.forEach(order => {
        expect(order.id).toMatch(/^PZA\d{3}$/)
      })
    })

    it('should have positive quantities', () => {
      mockPizzaOrders.forEach(order => {
        expect(order.quantity).toBeGreaterThan(0)
        expect(order.quantity).toBeLessThanOrEqual(5)
      })
    })

    it('should have valid status values', () => {
      const validStatuses = ['Pending', 'Preparing', 'Out for Delivery', 'Delivered', 'Cancelled']
      
      mockPizzaOrders.forEach(order => {
        expect(validStatuses).toContain(order.status)
      })
    })

    it('should have Indian customer names', () => {
      const indianNames = ['Arjun', 'Priya', 'Vikram', 'Ananya', 'Rohit', 'Sneha']
      const hasIndianNames = mockPizzaOrders.some(order => 
        indianNames.some(name => order.customerName.includes(name))
      )
      expect(hasIndianNames).toBe(true)
    })

    it('should have Indian pizza types', () => {
      const indianPizzas = ['Paneer Tikka', 'Chicken Tandoori', 'Butter Chicken', 'Paneer Makhani']
      const hasIndianPizzas = mockPizzaOrders.some(order => 
        indianPizzas.some(pizza => order.pizzaType.includes(pizza))
      )
      expect(hasIndianPizzas).toBe(true)
    })

    it('should have valid date format', () => {
      const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/
      
      mockPizzaOrders.forEach(order => {
        expect(order.orderDate).toMatch(dateRegex)
      })
    })
  })

  describe('getStatusConfig', () => {
    it('should return correct config for Pending status', () => {
      const config = getStatusConfig('Pending')
      
      expect(config.color).toContain('bg-amber-100')
      expect(config.color).toContain('text-amber-800')
      expect(config.icon).toBe('⏳')
      expect(config.dotColor).toBe('bg-amber-400')
    })

    it('should return correct config for Preparing status', () => {
      const config = getStatusConfig('Preparing')
      
      expect(config.color).toContain('bg-blue-100')
      expect(config.icon).toBe('👨‍🍳')
      expect(config.dotColor).toBe('bg-blue-400')
    })

    it('should return correct config for Out for Delivery status', () => {
      const config = getStatusConfig('Out for Delivery')
      
      expect(config.color).toContain('bg-purple-100')
      expect(config.icon).toBe('🚚')
      expect(config.dotColor).toBe('bg-purple-400')
    })

    it('should return correct config for Delivered status', () => {
      const config = getStatusConfig('Delivered')
      
      expect(config.color).toContain('bg-emerald-100')
      expect(config.icon).toBe('✅')
      expect(config.dotColor).toBe('bg-emerald-400')
    })

    it('should return correct config for Cancelled status', () => {
      const config = getStatusConfig('Cancelled')
      
      expect(config.color).toContain('bg-red-100')
      expect(config.icon).toBe('❌')
      expect(config.dotColor).toBe('bg-red-400')
    })

    it('should return default config for unknown status', () => {
      // @ts-ignore - Testing invalid status
      const config = getStatusConfig('Unknown')
      
      expect(config.color).toContain('bg-gray-100')
      expect(config.icon).toBe('❓')
      expect(config.dotColor).toBe('bg-gray-400')
    })
  })
})
