[{"name": "hot-reloader", "duration": 107, "timestamp": 503795513503, "id": 3, "tags": {"version": "15.1.8"}, "startTime": 1748072308170, "traceId": "37d984b151023c43"}, {"name": "setup-dev-bundler", "duration": 17321964, "timestamp": 503793393856, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748072306050, "traceId": "37d984b151023c43"}, {"name": "run-instrumentation-hook", "duration": 62, "timestamp": 503811563346, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748072324220, "traceId": "37d984b151023c43"}, {"name": "start-dev-server", "duration": 23094559, "timestamp": 503788517576, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "542220288", "memory.totalMem": "6349565952", "memory.heapSizeLimit": "3224371200", "memory.rss": "257032192", "memory.heapTotal": "95207424", "memory.heapUsed": "69244912"}, "startTime": 1748072301174, "traceId": "37d984b151023c43"}, {"name": "compile-path", "duration": 28811938, "timestamp": 503913552503, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748072426209, "traceId": "37d984b151023c43"}, {"name": "ensure-page", "duration": 28827352, "timestamp": 503913541621, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748072426201, "traceId": "37d984b151023c43"}]