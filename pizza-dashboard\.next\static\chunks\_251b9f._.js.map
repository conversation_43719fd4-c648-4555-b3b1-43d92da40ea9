{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/auth/signin/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { signIn, getSession } from \"next-auth/react\";\nimport { useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function SignIn() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [mounted, setMounted] = useState(false);\n  const router = useRouter();\n\n  // Check if Google OAuth is configured\n  const isOAuthConfigured = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID !== undefined;\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    // Check if user is already signed in\n    getSession().then((session) => {\n      if (session) {\n        router.push(\"/dashboard\");\n      }\n    });\n  }, [router, mounted]);\n\n  const handleGoogleSignIn = async () => {\n    setIsLoading(true);\n    try {\n      const result = await signIn(\"google\", {\n        callbackUrl: \"/dashboard\",\n        redirect: false,\n      });\n\n      if (result?.ok) {\n        router.push(\"/dashboard\");\n      }\n    } catch (error) {\n      console.error(\"Sign in error:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-red-50\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-12 w-12 bg-orange-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-2xl\">🍕</span>\n          </div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Welcome to Pizza Dashboard\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to manage your pizza orders\n          </p>\n        </div>\n\n        <div className=\"mt-8 space-y-6\">\n          {/* Configuration Notice */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-blue-800\">\n                  Google OAuth Setup Required\n                </h3>\n                <div className=\"mt-2 text-sm text-blue-700\">\n                  <p>To enable Google sign-in, please:</p>\n                  <ol className=\"list-decimal list-inside mt-2 space-y-1\">\n                    <li>Go to <a href=\"https://console.cloud.google.com/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline\">Google Cloud Console</a></li>\n                    <li>Create OAuth 2.0 credentials</li>\n                    <li>Add redirect URI: <code className=\"bg-blue-100 px-1 rounded\">http://localhost:3000/api/auth/callback/google</code></li>\n                    <li>Update your <code className=\"bg-blue-100 px-1 rounded\">.env.local</code> file with the credentials</li>\n                  </ol>\n                  <p className=\"mt-2\">See README.md for detailed instructions.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <button\n            onClick={handleGoogleSignIn}\n            disabled={isLoading}\n            className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {isLoading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Signing in...\n              </div>\n            ) : (\n              <div className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                Continue with Google\n              </div>\n            )}\n          </button>\n\n          {/* Demo Mode Button */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-gradient-to-br from-orange-50 to-red-50 text-gray-500\">Or for demo purposes</span>\n            </div>\n          </div>\n\n          <button\n            onClick={() => router.push(\"/dashboard\")}\n            className=\"w-full flex justify-center py-3 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\"\n          >\n            <div className=\"flex items-center\">\n              <span className=\"text-lg mr-2\">🎭</span>\n              View Demo Dashboard\n            </div>\n          </button>\n\n          <div className=\"text-center\">\n            <p className=\"text-xs text-gray-500\">\n              Demo mode allows you to explore the dashboard without authentication\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQ4B;;;AAZ5B;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,sCAAsC;IACtC,MAAM,oBAAoB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK;IAEvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,SAAS;YAEd,qCAAqC;YACrC,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,IAAI,IAAI;oCAAC,CAAC;oBACjB,IAAI,SAAS;wBACX,OAAO,IAAI,CAAC;oBACd;gBACF;;QACF;2BAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBACpC,aAAa;gBACb,UAAU;YACZ;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;sCAExC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,SAAQ;4CAAY,MAAK;sDAC9D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAmI,UAAS;;;;;;;;;;;;;;;;kDAG3K,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAE;;;;;;kEACH,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;;oEAAG;kFAAM,6LAAC;wEAAE,MAAK;wEAAoC,QAAO;wEAAS,KAAI;wEAAsB,WAAU;kFAAY;;;;;;;;;;;;0EACtH,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;;oEAAG;kFAAkB,6LAAC;wEAAK,WAAU;kFAA2B;;;;;;;;;;;;0EACjE,6LAAC;;oEAAG;kFAAY,6LAAC;wEAAK,WAAU;kFAA2B;;;;;;oEAAiB;;;;;;;;;;;;;kEAE9E,6LAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;;;;;qDAIxF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAe,SAAQ;;0DACpC,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;0DAEJ,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;;;;;;;oCAEA;;;;;;;;;;;;sCAOZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAgE;;;;;;;;;;;;;;;;;sCAIpF,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAAS;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAnJwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF"}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}