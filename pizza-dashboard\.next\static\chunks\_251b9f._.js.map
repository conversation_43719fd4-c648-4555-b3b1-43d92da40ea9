{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/auth/signin/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { signIn, getSession } from \"next-auth/react\";\nimport { useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nfunction SignInContent() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is already signed in\n    getSession().then((session) => {\n      if (session) {\n        router.push(\"/dashboard\");\n      }\n    });\n  }, [router]);\n\n  const handleGoogleSignIn = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn(\"google\", {\n        callbackUrl: \"/dashboard\",\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError(\"Authentication failed. Please try again.\");\n      } else if (result?.ok) {\n        router.push(\"/dashboard\");\n      }\n    } catch (error) {\n      console.error(\"Sign in error:\", error);\n      setError(\"An unexpected error occurred. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-red-50\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-12 w-12 bg-orange-500 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-2xl\">🍕</span>\n          </div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Welcome to Pizza Dashboard\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to manage your pizza orders\n          </p>\n        </div>\n\n        <div className=\"mt-8 space-y-6\">\n          {/* Error Message */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-red-800\">\n                    Authentication Error\n                  </h3>\n                  <div className=\"mt-2 text-sm text-red-700\">\n                    <p>{error}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n\n\n          <button\n            onClick={handleGoogleSignIn}\n            disabled={isLoading}\n            className={`w-full flex justify-center py-4 px-6 border border-transparent text-base font-medium rounded-lg text-white transition-all duration-200 transform ${\n              isLoading\n                ? \"bg-gray-400 cursor-not-allowed\"\n                : \"bg-orange-600 hover:bg-orange-700 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 shadow-lg hover:shadow-xl\"\n            }`}\n          >\n            {isLoading ? (\n              <div className=\"flex items-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Signing in...\n              </div>\n            ) : (\n              <div className=\"flex items-center\">\n                <svg className=\"w-6 h-6 mr-3\" viewBox=\"0 0 24 24\">\n                  <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                  <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                  <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                  <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                </svg>\n                Continue with Google\n              </div>\n            )}\n          </button>\n\n          {/* Information Text */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Sign in or create an account using your Google credentials\n            </p>\n            <p className=\"text-xs text-gray-500 mt-2\">\n              New users will automatically have an account created\n            </p>\n          </div>\n\n          {/* Security Notice */}\n          <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-gray-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-gray-800\">\n                  Secure Authentication\n                </h3>\n                <div className=\"mt-1 text-sm text-gray-600\">\n                  <p>Your data is protected with Google's secure OAuth 2.0 authentication.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function SignIn() {\n  return <SignInContent />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,qCAAqC;YACrC,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,IAAI,IAAI;2CAAC,CAAC;oBACjB,IAAI,SAAS;wBACX,OAAO,IAAI,CAAC;oBACd;gBACF;;QACF;kCAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB;QACzB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBACpC,aAAa;gBACb,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO,IAAI,QAAQ,IAAI;gBACrB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;sCAExC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;wBAEZ,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASd,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,iJAAiJ,EAC3J,YACI,mCACA,yJACJ;sCAED,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,6LAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,6LAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;qDAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAe,SAAQ;;0DACpC,6LAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,6LAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,6LAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;0DAC5B,6LAAC;gDAAK,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCACxB;;;;;;;;;;;;sCAOZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,SAAQ;4CAAY,MAAK;sDAC9D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAyG,UAAS;;;;;;;;;;;;;;;;kDAGjJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrB;GA5IS;;QAGQ,qIAAA,CAAA,YAAS;;;KAHjB;AA8IM,SAAS;IACtB,qBAAO,6LAAC;;;;;AACV;MAFwB"}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}