import { render, screen } from '@testing-library/react'
import { StatusBadge } from '../StatusBadge'

describe('StatusBadge', () => {
  it('renders pending status correctly', () => {
    render(<StatusBadge status="Pending" />)
    
    expect(screen.getByText('Pending')).toBeInTheDocument()
    expect(screen.getByText('⏳')).toBeInTheDocument()
  })

  it('renders preparing status correctly', () => {
    render(<StatusBadge status="Preparing" />)
    
    expect(screen.getByText('Preparing')).toBeInTheDocument()
    expect(screen.getByText('👨‍🍳')).toBeInTheDocument()
  })

  it('renders out for delivery status correctly', () => {
    render(<StatusBadge status="Out for Delivery" />)
    
    expect(screen.getByText('Out for Delivery')).toBeInTheDocument()
    expect(screen.getByText('🚚')).toBeInTheDocument()
  })

  it('renders delivered status correctly', () => {
    render(<StatusBadge status="Delivered" />)
    
    expect(screen.getByText('Delivered')).toBeInTheDocument()
    expect(screen.getByText('✅')).toBeInTheDocument()
  })

  it('renders cancelled status correctly', () => {
    render(<StatusBadge status="Cancelled" />)
    
    expect(screen.getByText('Cancelled')).toBeInTheDocument()
    expect(screen.getByText('❌')).toBeInTheDocument()
  })

  it('renders without icon when showIcon is false', () => {
    render(<StatusBadge status="Pending" showIcon={false} />)
    
    expect(screen.getByText('Pending')).toBeInTheDocument()
    expect(screen.queryByText('⏳')).not.toBeInTheDocument()
  })

  it('renders without dot when showDot is false', () => {
    const { container } = render(<StatusBadge status="Pending" showDot={false} />)
    
    expect(screen.getByText('Pending')).toBeInTheDocument()
    expect(container.querySelector('.animate-pulse')).not.toBeInTheDocument()
  })

  it('applies correct size classes', () => {
    const { container } = render(<StatusBadge status="Pending" size="lg" />)
    
    expect(container.firstChild).toHaveClass('px-4', 'py-2', 'text-base')
  })

  it('applies correct color classes for each status', () => {
    const { container: pendingContainer } = render(<StatusBadge status="Pending" />)
    expect(pendingContainer.firstChild).toHaveClass('bg-amber-100', 'text-amber-800')

    const { container: preparingContainer } = render(<StatusBadge status="Preparing" />)
    expect(preparingContainer.firstChild).toHaveClass('bg-blue-100', 'text-blue-800')

    const { container: deliveredContainer } = render(<StatusBadge status="Delivered" />)
    expect(deliveredContainer.firstChild).toHaveClass('bg-emerald-100', 'text-emerald-800')
  })
})
