{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport GoogleProvider from \"next-auth/providers/google\";\n\nconst handler = NextAuth({\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n  ],\n  callbacks: {\n    async session({ session, token }) {\n      return session;\n    },\n    async jwt({ token, user }) {\n      return token;\n    },\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n  },\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE;IACvB,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF"}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}