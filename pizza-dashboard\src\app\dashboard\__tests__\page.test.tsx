import { render, screen } from '@testing-library/react'
import Dashboard from '../page'

// Mock the StatusBadge component
jest.mock('../../components/StatusBadge', () => ({
  StatusBadge: ({ status }: { status: string }) => <span data-testid="status-badge">{status}</span>
}))

describe('Dashboard Page', () => {
  it('renders welcome message with user name', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Welcome to PizzaFlow')).toBeInTheDocument()
    expect(screen.getByText('Hello, Test User!')).toBeInTheDocument()
  })

  it('displays order statistics', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Total Orders')).toBeInTheDocument()
    expect(screen.getByText('28')).toBeInTheDocument()
    expect(screen.getByText('Delivered')).toBeInTheDocument()
    expect(screen.getByText('9')).toBeInTheDocument()
    expect(screen.getByText('Revenue')).toBeInTheDocument()
    expect(screen.getByText('₹45,280')).toBeInTheDocument()
  })

  it('displays status overview section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Order Status Overview')).toBeInTheDocument()
    
    // Check for status badges
    const statusBadges = screen.getAllByTestId('status-badge')
    expect(statusBadges).toHaveLength(5)
    
    // Check for order counts
    expect(screen.getByText('5 orders')).toBeInTheDocument()
    expect(screen.getByText('6 orders')).toBeInTheDocument()
    expect(screen.getByText('9 orders')).toBeInTheDocument()
    expect(screen.getByText('2 orders')).toBeInTheDocument()
  })

  it('displays quick actions section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Quick Actions')).toBeInTheDocument()
    expect(screen.getByText('View All Orders')).toBeInTheDocument()
    expect(screen.getByText('Add New Order')).toBeInTheDocument()
    expect(screen.getByText('Generate Report')).toBeInTheDocument()
  })

  it('has proper navigation links', () => {
    render(<Dashboard />)
    
    const viewOrdersLink = screen.getByRole('link', { name: /view all orders/i })
    expect(viewOrdersLink).toHaveAttribute('href', '/dashboard/orders')
  })

  it('displays recent activity section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    expect(screen.getByText('New order from Arjun Sharma')).toBeInTheDocument()
    expect(screen.getByText('Order PZA024 delivered')).toBeInTheDocument()
    expect(screen.getByText('Payment received for PZA023')).toBeInTheDocument()
  })

  it('displays performance metrics', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Performance')).toBeInTheDocument()
    expect(screen.getByText('Average delivery time: 28 mins')).toBeInTheDocument()
    expect(screen.getByText('Customer satisfaction: 4.8/5')).toBeInTheDocument()
    expect(screen.getByText('Orders completed today: 12')).toBeInTheDocument()
  })

  it('has responsive design classes', () => {
    const { container } = render(<Dashboard />)
    
    // Check for responsive grid classes
    expect(container.querySelector('.grid')).toBeInTheDocument()
    expect(container.querySelector('.sm\\:grid-cols-2')).toBeInTheDocument()
    expect(container.querySelector('.lg\\:grid-cols-3')).toBeInTheDocument()
  })
})
