{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/lib/mockData.ts"], "sourcesContent": ["export interface PizzaOrder {\n  id: string;\n  customerName: string;\n  pizzaType: string;\n  quantity: number;\n  orderDate: string;\n  status: 'Pending' | 'Preparing' | 'Out for Delivery' | 'Delivered' | 'Cancelled';\n}\n\nexport const mockPizzaOrders: PizzaOrder[] = [\n  {\n    id: \"PZA001\",\n    customerName: \"<PERSON><PERSON><PERSON>\",\n    pizzaType: \"Margheri<PERSON>\",\n    quantity: 2,\n    orderDate: \"2024-01-15 14:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA002\",\n    customerName: \"<PERSON><PERSON> Patel\",\n    pizzaType: \"Pepperoni\",\n    quantity: 1,\n    orderDate: \"2024-01-15 15:45\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA003\",\n    customerName: \"<PERSON><PERSON><PERSON>\",\n    pizzaType: \"Veggie Supreme\",\n    quantity: 3,\n    orderDate: \"2024-01-15 16:20\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA004\",\n    customerName: \"Ana<PERSON> Gupta\",\n    pizzaType: \"Paneer Tikka\",\n    quantity: 1,\n    orderDate: \"2024-01-15 17:10\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA005\",\n    customerName: \"<PERSON><PERSON><PERSON> Kumar\",\n    pizzaType: \"Chicken Tandoori\",\n    quantity: 2,\n    orderDate: \"2024-01-15 18:00\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA006\",\n    customerName: \"Sneha Reddy\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-15 18:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA007\",\n    customerName: \"Karan Mehta\",\n    pizzaType: \"Four Cheese\",\n    quantity: 2,\n    orderDate: \"2024-01-15 19:15\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA008\",\n    customerName: \"Kavya Iyer\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-15 19:45\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA009\",\n    customerName: \"Aditya Joshi\",\n    pizzaType: \"Pepperoni\",\n    quantity: 3,\n    orderDate: \"2024-01-15 20:20\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA010\",\n    customerName: \"Riya Agarwal\",\n    pizzaType: \"Veggie Supreme\",\n    quantity: 1,\n    orderDate: \"2024-01-15 20:50\",\n    status: \"Cancelled\"\n  },\n  {\n    id: \"PZA011\",\n    customerName: \"Siddharth Rao\",\n    pizzaType: \"Chicken Keema\",\n    quantity: 2,\n    orderDate: \"2024-01-16 12:15\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA012\",\n    customerName: \"Pooja Nair\",\n    pizzaType: \"Paneer Makhani\",\n    quantity: 1,\n    orderDate: \"2024-01-16 13:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA013\",\n    customerName: \"Aryan Malhotra\",\n    pizzaType: \"Chicken Tikka\",\n    quantity: 2,\n    orderDate: \"2024-01-16 14:45\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA014\",\n    customerName: \"Ishita Bansal\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-16 15:20\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA015\",\n    customerName: \"Nikhil Verma\",\n    pizzaType: \"Four Cheese\",\n    quantity: 3,\n    orderDate: \"2024-01-16 16:10\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA016\",\n    customerName: \"Shreya Kapoor\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-16 17:00\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA017\",\n    customerName: \"Varun Chopra\",\n    pizzaType: \"Pepperoni\",\n    quantity: 2,\n    orderDate: \"2024-01-16 18:15\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA018\",\n    customerName: \"Divya Sinha\",\n    pizzaType: \"Veggie Delight\",\n    quantity: 1,\n    orderDate: \"2024-01-16 19:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA019\",\n    customerName: \"Akash Tiwari\",\n    pizzaType: \"Chicken Dominator\",\n    quantity: 2,\n    orderDate: \"2024-01-16 20:00\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA020\",\n    customerName: \"Meera Saxena\",\n    pizzaType: \"Paneer Tikka\",\n    quantity: 1,\n    orderDate: \"2024-01-16 20:45\",\n    status: \"Cancelled\"\n  },\n  {\n    id: \"PZA021\",\n    customerName: \"Rahul Bhardwaj\",\n    pizzaType: \"Chicken Tandoori\",\n    quantity: 3,\n    orderDate: \"2024-01-17 11:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA022\",\n    customerName: \"Nisha Aggarwal\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-17 12:45\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA023\",\n    customerName: \"Harsh Goyal\",\n    pizzaType: \"Cheese Burst\",\n    quantity: 2,\n    orderDate: \"2024-01-17 13:20\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA024\",\n    customerName: \"Tanvi Mishra\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-17 14:10\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA025\",\n    customerName: \"Rajesh Khanna\",\n    pizzaType: \"Achari Paneer\",\n    quantity: 2,\n    orderDate: \"2024-01-17 15:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA026\",\n    customerName: \"Deepika Sharma\",\n    pizzaType: \"Butter Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-17 16:45\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA027\",\n    customerName: \"Amit Agrawal\",\n    pizzaType: \"Spicy Chicken Tikka\",\n    quantity: 3,\n    orderDate: \"2024-01-17 17:20\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA028\",\n    customerName: \"Sunita Devi\",\n    pizzaType: \"Paneer Makhani\",\n    quantity: 1,\n    orderDate: \"2024-01-17 18:15\",\n    status: \"Out for Delivery\"\n  }\n];\n\nexport const getStatusConfig = (status: PizzaOrder['status']) => {\n  switch (status) {\n    case 'Pending':\n      return {\n        color: 'bg-amber-100 text-amber-800 border-amber-200',\n        icon: '⏳',\n        dotColor: 'bg-amber-400'\n      };\n    case 'Preparing':\n      return {\n        color: 'bg-blue-100 text-blue-800 border-blue-200',\n        icon: '👨‍🍳',\n        dotColor: 'bg-blue-400'\n      };\n    case 'Out for Delivery':\n      return {\n        color: 'bg-purple-100 text-purple-800 border-purple-200',\n        icon: '🚚',\n        dotColor: 'bg-purple-400'\n      };\n    case 'Delivered':\n      return {\n        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',\n        icon: '✅',\n        dotColor: 'bg-emerald-400'\n      };\n    case 'Cancelled':\n      return {\n        color: 'bg-red-100 text-red-800 border-red-200',\n        icon: '❌',\n        dotColor: 'bg-red-400'\n      };\n    default:\n      return {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: '❓',\n        dotColor: 'bg-gray-400'\n      };\n  }\n};\n"], "names": [], "mappings": ";;;;AASO,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;CACD;AAEM,MAAM,kBAAkB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF;YACE,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;IACJ;AACF"}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/components/StatusBadge.tsx"], "sourcesContent": ["import { getStatusConfig, PizzaOrder } from \"@/lib/mockData\";\n\ninterface StatusBadgeProps {\n  status: PizzaOrder['status'];\n  showIcon?: boolean;\n  showDot?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport function StatusBadge({ \n  status, \n  showIcon = true, \n  showDot = true, \n  size = 'md' \n}: StatusBadgeProps) {\n  const config = getStatusConfig(status);\n  \n  const sizeClasses = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center gap-1.5 font-semibold rounded-full border\n      ${config.color} ${sizeClasses[size]}\n      transition-all duration-200 hover:shadow-sm\n    `}>\n      {showDot && (\n        <span className={`w-2 h-2 rounded-full ${config.dotColor} animate-pulse`} />\n      )}\n      {showIcon && (\n        <span className=\"text-sm\">{config.icon}</span>\n      )}\n      <span className=\"font-medium\">{status}</span>\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AASO,SAAS,YAAY,EAC1B,MAAM,EACN,WAAW,IAAI,EACf,UAAU,IAAI,EACd,OAAO,IAAI,EACM;IACjB,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;IAE/B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAK,WAAW,CAAC;;MAEhB,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC;;IAEtC,CAAC;;YACE,yBACC,6LAAC;gBAAK,WAAW,CAAC,qBAAqB,EAAE,OAAO,QAAQ,CAAC,cAAc,CAAC;;;;;;YAEzE,0BACC,6LAAC;gBAAK,WAAU;0BAAW,OAAO,IAAI;;;;;;0BAExC,6LAAC;gBAAK,WAAU;0BAAe;;;;;;;;;;;;AAGrC;KA7BgB"}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/dashboard/orders/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo } from \"react\";\nimport { mockPizzaOrders, PizzaOrder } from \"@/lib/mockData\";\nimport { StatusBadge } from \"@/app/components/StatusBadge\";\n\ntype SortField = keyof PizzaOrder;\ntype SortDirection = 'asc' | 'desc';\n\nexport default function OrdersPage() {\n  const [sortField, setSortField] = useState<SortField>('orderDate');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const handleSort = (field: SortField) => {\n    if (field === sortField) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  const filteredAndSortedOrders = useMemo(() => {\n    let filtered = mockPizzaOrders;\n\n    // Filter by status\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(order => order.status === statusFilter);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(order =>\n        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.pizzaType.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    // Sort\n    return filtered.sort((a, b) => {\n      let aValue = a[sortField];\n      let bValue = b[sortField];\n\n      if (sortField === 'orderDate') {\n        aValue = new Date(aValue as string).getTime();\n        bValue = new Date(bValue as string).getTime();\n      }\n\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (aValue < bValue) {\n        return sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n  }, [sortField, sortDirection, statusFilter, searchTerm]);\n\n  const getSortIcon = (field: SortField) => {\n    if (sortField !== field) {\n      return (\n        <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4\" />\n        </svg>\n      );\n    }\n\n    return sortDirection === 'asc' ? (\n      <svg className=\"w-4 h-4 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12\" />\n      </svg>\n    ) : (\n      <svg className=\"w-4 h-4 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4\" />\n      </svg>\n    );\n  };\n\n  const statusOptions = [\n    { value: 'all', label: 'All Status' },\n    { value: 'Pending', label: 'Pending' },\n    { value: 'Preparing', label: 'Preparing' },\n    { value: 'Out for Delivery', label: 'Out for Delivery' },\n    { value: 'Delivered', label: 'Delivered' },\n    { value: 'Cancelled', label: 'Cancelled' },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"md:flex md:items-center md:justify-between\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Pizza Orders\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Manage and track all pizza orders ({filteredAndSortedOrders.length} orders)\n          </p>\n        </div>\n      </div>\n\n      {/* Status Legend */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Order Status Guide</h3>\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3\">\n          <div className=\"flex items-center space-x-2\">\n            <StatusBadge status=\"Pending\" size=\"sm\" />\n            <span className=\"text-xs text-gray-600\">Awaiting preparation</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <StatusBadge status=\"Preparing\" size=\"sm\" />\n            <span className=\"text-xs text-gray-600\">Being made</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <StatusBadge status=\"Out for Delivery\" size=\"sm\" />\n            <span className=\"text-xs text-gray-600\">On the way</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <StatusBadge status=\"Delivered\" size=\"sm\" />\n            <span className=\"text-xs text-gray-600\">Completed</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <StatusBadge status=\"Cancelled\" size=\"sm\" />\n            <span className=\"text-xs text-gray-600\">Order cancelled</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n          <div>\n            <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Orders\n            </label>\n            <input\n              type=\"text\"\n              id=\"search\"\n              placeholder=\"Search by customer, order ID, or pizza type...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"status-filter\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Filter by Status\n            </label>\n            <select\n              id=\"status-filter\"\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm\"\n            >\n              {statusOptions.map((option) => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm('');\n                setStatusFilter('all');\n                setSortField('orderDate');\n                setSortDirection('desc');\n              }}\n              className=\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              Clear Filters\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Orders Table */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('id')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Order ID</span>\n                    {getSortIcon('id')}\n                  </div>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('customerName')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Customer</span>\n                    {getSortIcon('customerName')}\n                  </div>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('pizzaType')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Pizza Type</span>\n                    {getSortIcon('pizzaType')}\n                  </div>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('quantity')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Quantity</span>\n                    {getSortIcon('quantity')}\n                  </div>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('orderDate')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Order Date</span>\n                    {getSortIcon('orderDate')}\n                  </div>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('status')}\n                >\n                  <div className=\"flex items-center space-x-1\">\n                    <span>Status</span>\n                    {getSortIcon('status')}\n                  </div>\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredAndSortedOrders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    {order.id}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.customerName}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.pizzaType}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {order.quantity}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(order.orderDate).toLocaleString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <StatusBadge status={order.status} />\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {filteredAndSortedOrders.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 text-lg mb-2\">🍕</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No orders found</h3>\n            <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AASe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,WAAW;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YACtC,IAAI,WAAW,yHAAA,CAAA,kBAAe;YAE9B,mBAAmB;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,WAAW,SAAS,MAAM;mEAAC,CAAA,QAAS,MAAM,MAAM,KAAK;;YACvD;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;mEAAC,CAAA,QACzB,MAAM,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,MAAM,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,MAAM,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAEjE;YAEA,OAAO;YACP,OAAO,SAAS,IAAI;+DAAC,CAAC,GAAG;oBACvB,IAAI,SAAS,CAAC,CAAC,UAAU;oBACzB,IAAI,SAAS,CAAC,CAAC,UAAU;oBAEzB,IAAI,cAAc,aAAa;wBAC7B,SAAS,IAAI,KAAK,QAAkB,OAAO;wBAC3C,SAAS,IAAI,KAAK,QAAkB,OAAO;oBAC7C;oBAEA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;wBAC5D,SAAS,OAAO,WAAW;wBAC3B,SAAS,OAAO,WAAW;oBAC7B;oBAEA,IAAI,SAAS,QAAQ;wBACnB,OAAO,kBAAkB,QAAQ,CAAC,IAAI;oBACxC;oBACA,IAAI,SAAS,QAAQ;wBACnB,OAAO,kBAAkB,QAAQ,IAAI,CAAC;oBACxC;oBACA,OAAO;gBACT;;QACF;sDAAG;QAAC;QAAW;QAAe;QAAc;KAAW;IAEvD,MAAM,cAAc,CAAC;QACnB,IAAI,cAAc,OAAO;YACvB,qBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QAEA,OAAO,kBAAkB,sBACvB,6LAAC;YAAI,WAAU;YAA0B,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjF,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;iCAGvE,6LAAC;YAAI,WAAU;YAA0B,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjF,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAa;QACpC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAoB,OAAO;QAAmB;QACvD;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAa,OAAO;QAAY;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqE;;;;;;sCAGnF,6LAAC;4BAAE,WAAU;;gCAA6B;gCACJ,wBAAwB,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAU,MAAK;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAY,MAAK;;;;;;kDACrC,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAmB,MAAK;;;;;;kDAC5C,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAY,MAAK;;;;;;kDACrC,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAY,MAAK;;;;;;kDACrC,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAA+C;;;;;;8CAGjF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAgB,WAAU;8CAA+C;;;;;;8CAGxF,6LAAC;oCACC,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;8CAET,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4CAA0B,OAAO,OAAO,KAAK;sDAC3C,OAAO,KAAK;2CADF,OAAO,KAAK;;;;;;;;;;;;;;;;sCAO/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,cAAc;oCACd,gBAAgB;oCAChB,aAAa;oCACb,iBAAiB;gCACnB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,SAAS,IAAM,WAAW;0DAE1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;wDACL,YAAY;;;;;;;;;;;;;;;;;;;;;;;8CAKrB,6LAAC;oCAAM,WAAU;8CACd,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAG,WAAU;8DACX,MAAM,EAAE;;;;;;8DAEX,6LAAC;oDAAG,WAAU;8DACX,MAAM,YAAY;;;;;;8DAErB,6LAAC;oDAAG,WAAU;8DACX,MAAM,SAAS;;;;;;8DAElB,6LAAC;oDAAG,WAAU;8DACX,MAAM,QAAQ;;;;;;8DAEjB,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;8DAE3C,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC,2IAAA,CAAA,cAAW;wDAAC,QAAQ,MAAM,MAAM;;;;;;;;;;;;2CAjB5B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;oBAyBxB,wBAAwB,MAAM,KAAK,mBAClC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GA3RwB;KAAA"}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}