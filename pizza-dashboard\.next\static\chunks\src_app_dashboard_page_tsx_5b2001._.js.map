{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport Link from \"next/link\";\nimport { StatusBadge } from \"@/app/components/StatusBadge\";\n\nfunction DashboardContent() {\n  const { data: session } = useSession();\n\n  // Demo mode: show demo user if no session\n  const displayUser = session?.user || {\n    name: \"Demo User\",\n    email: \"<EMAIL>\"\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"h-16 w-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-3xl\">👋</span>\n              </div>\n            </div>\n            <div className=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                  Welcome back\n                </dt>\n                <dd className=\"text-2xl font-bold text-gray-900\">\n                  Hello, {displayUser.name}!\n                  {!session && (\n                    <span className=\"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2\">\n                      Demo Mode\n                    </span>\n                  )}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">📊</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Total Orders\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">24</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-green-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">✅</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Delivered\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">18</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-yellow-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">⏳</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Pending\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">4</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-red-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">❌</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Cancelled\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">2</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Quick Actions\n          </h3>\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n            <Link\n              href=\"/dashboard/orders\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-orange-50 text-orange-600 ring-4 ring-white\">\n                  <span className=\"text-2xl\">🍕</span>\n                </span>\n              </div>\n              <div className=\"mt-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  View Pizza Orders\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Manage and track all pizza orders in the system\n                </p>\n              </div>\n              <span\n                className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\"\n                aria-hidden=\"true\"\n              >\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z\" />\n                </svg>\n              </span>\n            </Link>\n\n            <div className=\"relative group bg-white p-6 rounded-lg border border-gray-200 opacity-50\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-gray-50 text-gray-400 ring-4 ring-white\">\n                  <span className=\"text-2xl\">📈</span>\n                </span>\n              </div>\n              <div className=\"mt-4\">\n                <h3 className=\"text-lg font-medium text-gray-400\">\n                  Analytics (Coming Soon)\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  View detailed analytics and reports\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Dashboard() {\n  return <DashboardContent />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,SAAS;;IACP,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,0CAA0C;IAC1C,MAAM,cAAc,SAAS,QAAQ;QACnC,MAAM;QACN,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAG,WAAU;;gDAAmC;gDACvC,YAAY,IAAI;gDAAC;gDACxB,CAAC,yBACA,6LAAC;oDAAK,WAAU;8DAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY9F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;sDACC,cAAA,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAI5C,6LAAC;4CACC,WAAU;4CACV,eAAY;sDAEZ,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D;GAhLS;;QACmB,iJAAA,CAAA,aAAU;;;KAD7B;AAkLM,SAAS;IACtB,qBAAO,6LAAC;;;;;AACV;MAFwB"}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}