{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/lib/mockData.ts"], "sourcesContent": ["export interface PizzaOrder {\n  id: string;\n  customerName: string;\n  pizzaType: string;\n  quantity: number;\n  orderDate: string;\n  status: 'Pending' | 'Preparing' | 'Out for Delivery' | 'Delivered' | 'Cancelled';\n}\n\nexport const mockPizzaOrders: PizzaOrder[] = [\n  {\n    id: \"PZA001\",\n    customerName: \"<PERSON><PERSON><PERSON>\",\n    pizzaType: \"Margheri<PERSON>\",\n    quantity: 2,\n    orderDate: \"2024-01-15 14:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA002\",\n    customerName: \"<PERSON><PERSON> Patel\",\n    pizzaType: \"Pepperoni\",\n    quantity: 1,\n    orderDate: \"2024-01-15 15:45\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA003\",\n    customerName: \"<PERSON><PERSON><PERSON>\",\n    pizzaType: \"Veggie Supreme\",\n    quantity: 3,\n    orderDate: \"2024-01-15 16:20\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA004\",\n    customerName: \"Ana<PERSON> Gupta\",\n    pizzaType: \"Paneer Tikka\",\n    quantity: 1,\n    orderDate: \"2024-01-15 17:10\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA005\",\n    customerName: \"<PERSON><PERSON><PERSON> Kumar\",\n    pizzaType: \"Chicken Tandoori\",\n    quantity: 2,\n    orderDate: \"2024-01-15 18:00\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA006\",\n    customerName: \"Sneha Reddy\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-15 18:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA007\",\n    customerName: \"Karan Mehta\",\n    pizzaType: \"Four Cheese\",\n    quantity: 2,\n    orderDate: \"2024-01-15 19:15\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA008\",\n    customerName: \"Kavya Iyer\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-15 19:45\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA009\",\n    customerName: \"Aditya Joshi\",\n    pizzaType: \"Pepperoni\",\n    quantity: 3,\n    orderDate: \"2024-01-15 20:20\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA010\",\n    customerName: \"Riya Agarwal\",\n    pizzaType: \"Veggie Supreme\",\n    quantity: 1,\n    orderDate: \"2024-01-15 20:50\",\n    status: \"Cancelled\"\n  },\n  {\n    id: \"PZA011\",\n    customerName: \"Siddharth Rao\",\n    pizzaType: \"Chicken Keema\",\n    quantity: 2,\n    orderDate: \"2024-01-16 12:15\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA012\",\n    customerName: \"Pooja Nair\",\n    pizzaType: \"Paneer Makhani\",\n    quantity: 1,\n    orderDate: \"2024-01-16 13:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA013\",\n    customerName: \"Aryan Malhotra\",\n    pizzaType: \"Chicken Tikka\",\n    quantity: 2,\n    orderDate: \"2024-01-16 14:45\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA014\",\n    customerName: \"Ishita Bansal\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-16 15:20\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA015\",\n    customerName: \"Nikhil Verma\",\n    pizzaType: \"Four Cheese\",\n    quantity: 3,\n    orderDate: \"2024-01-16 16:10\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA016\",\n    customerName: \"Shreya Kapoor\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-16 17:00\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA017\",\n    customerName: \"Varun Chopra\",\n    pizzaType: \"Pepperoni\",\n    quantity: 2,\n    orderDate: \"2024-01-16 18:15\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA018\",\n    customerName: \"Divya Sinha\",\n    pizzaType: \"Veggie Delight\",\n    quantity: 1,\n    orderDate: \"2024-01-16 19:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA019\",\n    customerName: \"Akash Tiwari\",\n    pizzaType: \"Chicken Dominator\",\n    quantity: 2,\n    orderDate: \"2024-01-16 20:00\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA020\",\n    customerName: \"Meera Saxena\",\n    pizzaType: \"Paneer Tikka\",\n    quantity: 1,\n    orderDate: \"2024-01-16 20:45\",\n    status: \"Cancelled\"\n  },\n  {\n    id: \"PZA021\",\n    customerName: \"Rahul Bhardwaj\",\n    pizzaType: \"Chicken Tandoori\",\n    quantity: 3,\n    orderDate: \"2024-01-17 11:30\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA022\",\n    customerName: \"Nisha Aggarwal\",\n    pizzaType: \"BBQ Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-17 12:45\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA023\",\n    customerName: \"Harsh Goyal\",\n    pizzaType: \"Cheese Burst\",\n    quantity: 2,\n    orderDate: \"2024-01-17 13:20\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA024\",\n    customerName: \"Tanvi Mishra\",\n    pizzaType: \"Margherita\",\n    quantity: 1,\n    orderDate: \"2024-01-17 14:10\",\n    status: \"Out for Delivery\"\n  },\n  {\n    id: \"PZA025\",\n    customerName: \"Rajesh Khanna\",\n    pizzaType: \"Achari Paneer\",\n    quantity: 2,\n    orderDate: \"2024-01-17 15:30\",\n    status: \"Preparing\"\n  },\n  {\n    id: \"PZA026\",\n    customerName: \"Deepika Sharma\",\n    pizzaType: \"Butter Chicken\",\n    quantity: 1,\n    orderDate: \"2024-01-17 16:45\",\n    status: \"Delivered\"\n  },\n  {\n    id: \"PZA027\",\n    customerName: \"Amit Agrawal\",\n    pizzaType: \"Spicy Chicken Tikka\",\n    quantity: 3,\n    orderDate: \"2024-01-17 17:20\",\n    status: \"Pending\"\n  },\n  {\n    id: \"PZA028\",\n    customerName: \"Sunita Devi\",\n    pizzaType: \"Paneer Makhani\",\n    quantity: 1,\n    orderDate: \"2024-01-17 18:15\",\n    status: \"Out for Delivery\"\n  }\n];\n\nexport const getStatusConfig = (status: PizzaOrder['status']) => {\n  switch (status) {\n    case 'Pending':\n      return {\n        color: 'bg-amber-100 text-amber-800 border-amber-200',\n        icon: '⏳',\n        dotColor: 'bg-amber-400'\n      };\n    case 'Preparing':\n      return {\n        color: 'bg-blue-100 text-blue-800 border-blue-200',\n        icon: '👨‍🍳',\n        dotColor: 'bg-blue-400'\n      };\n    case 'Out for Delivery':\n      return {\n        color: 'bg-purple-100 text-purple-800 border-purple-200',\n        icon: '🚚',\n        dotColor: 'bg-purple-400'\n      };\n    case 'Delivered':\n      return {\n        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',\n        icon: '✅',\n        dotColor: 'bg-emerald-400'\n      };\n    case 'Cancelled':\n      return {\n        color: 'bg-red-100 text-red-800 border-red-200',\n        icon: '❌',\n        dotColor: 'bg-red-400'\n      };\n    default:\n      return {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: '❓',\n        dotColor: 'bg-gray-400'\n      };\n  }\n};\n"], "names": [], "mappings": ";;;;AASO,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;QACX,QAAQ;IACV;CACD;AAEM,MAAM,kBAAkB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;QACF;YACE,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;IACJ;AACF"}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/components/StatusBadge.tsx"], "sourcesContent": ["import { getStatusConfig, PizzaOrder } from \"@/lib/mockData\";\n\ninterface StatusBadgeProps {\n  status: PizzaOrder['status'];\n  showIcon?: boolean;\n  showDot?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport function StatusBadge({ \n  status, \n  showIcon = true, \n  showDot = true, \n  size = 'md' \n}: StatusBadgeProps) {\n  const config = getStatusConfig(status);\n  \n  const sizeClasses = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center gap-1.5 font-semibold rounded-full border\n      ${config.color} ${sizeClasses[size]}\n      transition-all duration-200 hover:shadow-sm\n    `}>\n      {showDot && (\n        <span className={`w-2 h-2 rounded-full ${config.dotColor} animate-pulse`} />\n      )}\n      {showIcon && (\n        <span className=\"text-sm\">{config.icon}</span>\n      )}\n      <span className=\"font-medium\">{status}</span>\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AASO,SAAS,YAAY,EAC1B,MAAM,EACN,WAAW,IAAI,EACf,UAAU,IAAI,EACd,OAAO,IAAI,EACM;IACjB,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;IAE/B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAK,WAAW,CAAC;;MAEhB,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC;;IAEtC,CAAC;;YACE,yBACC,6LAAC;gBAAK,WAAW,CAAC,qBAAqB,EAAE,OAAO,QAAQ,CAAC,cAAc,CAAC;;;;;;YAEzE,0BACC,6LAAC;gBAAK,WAAU;0BAAW,OAAO,IAAI;;;;;;0BAExC,6LAAC;gBAAK,WAAU;0BAAe;;;;;;;;;;;;AAGrC;KA7BgB"}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport Link from \"next/link\";\nimport { StatusBadge } from \"@/app/components/StatusBadge\";\n\nfunction DashboardContent() {\n  const { data: session } = useSession();\n\n  if (!session) {\n    return null;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"h-16 w-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-3xl\">👋</span>\n              </div>\n            </div>\n            <div className=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                  Welcome back\n                </dt>\n                <dd className=\"text-2xl font-bold text-gray-900\">\n                  Hello, {session.user?.name}!\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">📊</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Total Orders\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">28</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-green-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">✅</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Delivered\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">9</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-yellow-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">⏳</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Pending\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">4</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-red-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">❌</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Cancelled\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">2</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Status Overview */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Order Status Overview\n          </h3>\n          <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4\">\n            <div className=\"text-center\">\n              <StatusBadge status=\"Pending\" size=\"sm\" showDot={false} />\n              <p className=\"text-sm text-gray-600 mt-1\">5 orders</p>\n            </div>\n            <div className=\"text-center\">\n              <StatusBadge status=\"Preparing\" size=\"sm\" showDot={false} />\n              <p className=\"text-sm text-gray-600 mt-1\">6 orders</p>\n            </div>\n            <div className=\"text-center\">\n              <StatusBadge status=\"Out for Delivery\" size=\"sm\" showDot={false} />\n              <p className=\"text-sm text-gray-600 mt-1\">6 orders</p>\n            </div>\n            <div className=\"text-center\">\n              <StatusBadge status=\"Delivered\" size=\"sm\" showDot={false} />\n              <p className=\"text-sm text-gray-600 mt-1\">9 orders</p>\n            </div>\n            <div className=\"text-center\">\n              <StatusBadge status=\"Cancelled\" size=\"sm\" showDot={false} />\n              <p className=\"text-sm text-gray-600 mt-1\">2 orders</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Quick Actions\n          </h3>\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n            <Link\n              href=\"/dashboard/orders\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-orange-50 text-orange-600 ring-4 ring-white\">\n                  <span className=\"text-2xl\">🍕</span>\n                </span>\n              </div>\n              <div className=\"mt-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  View Pizza Orders\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Manage and track all pizza orders in the system\n                </p>\n              </div>\n              <span\n                className=\"pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400\"\n                aria-hidden=\"true\"\n              >\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z\" />\n                </svg>\n              </span>\n            </Link>\n\n            <div className=\"relative group bg-white p-6 rounded-lg border border-gray-200 opacity-50\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-gray-50 text-gray-400 ring-4 ring-white\">\n                  <span className=\"text-2xl\">📈</span>\n                </span>\n              </div>\n              <div className=\"mt-4\">\n                <h3 className=\"text-lg font-medium text-gray-400\">\n                  Analytics (Coming Soon)\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  View detailed analytics and reports\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Dashboard() {\n  return <DashboardContent />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,SAAS;;IACP,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAG,WAAU;;gDAAmC;gDACvC,QAAQ,IAAI,EAAE;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,cAAW;4CAAC,QAAO;4CAAU,MAAK;4CAAK,SAAS;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,cAAW;4CAAC,QAAO;4CAAY,MAAK;4CAAK,SAAS;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,cAAW;4CAAC,QAAO;4CAAmB,MAAK;4CAAK,SAAS;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,cAAW;4CAAC,QAAO;4CAAY,MAAK;4CAAK,SAAS;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,cAAW;4CAAC,QAAO;4CAAY,MAAK;4CAAK,SAAS;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;sDACC,cAAA,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAI5C,6LAAC;4CACC,WAAU;4CACV,eAAY;sDAEZ,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D;GAxMS;;QACmB,iJAAA,CAAA,aAAU;;;KAD7B;AA0MM,SAAS;IACtB,qBAAO,6LAAC;;;;;AACV;MAFwB"}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}