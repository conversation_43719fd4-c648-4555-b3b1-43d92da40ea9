{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Home() {\n  const router = useRouter();\n\n  useEffect(() => {\n    router.push(\"/auth/signin\");\n  }, [router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,OAAO,IAAI,CAAC;QACd;yBAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;GAZwB;;QACP,qIAAA,CAAA,YAAS;;;KADF"}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Application-1/pizza-dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}