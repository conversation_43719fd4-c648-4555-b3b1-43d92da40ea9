import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { signIn } from 'next-auth/react'
import SignIn from '../page'

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
  getSession: jest.fn(),
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated'
  }))
}))

// Mock next/navigation
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('SignIn Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the sign-in page correctly', () => {
    render(<SignIn />)
    
    expect(screen.getByText('PizzaFlow')).toBeInTheDocument()
    expect(screen.getByText('Sign in to manage your orders')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /continue with google/i })).toBeInTheDocument()
  })

  it('displays the pizza logo', () => {
    render(<SignIn />)
    
    expect(screen.getByText('🍕')).toBeInTheDocument()
  })

  it('calls signIn when Google button is clicked', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
    mockSignIn.mockResolvedValue({ ok: true, error: null, status: 200, url: null })

    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('google', {
        callbackUrl: '/dashboard',
        redirect: false,
      })
    })
  })

  it('shows loading state when signing in', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
    mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    fireEvent.click(googleButton)

    expect(screen.getByText('Signing in...')).toBeInTheDocument()
    expect(googleButton).toBeDisabled()
  })

  it('displays error message when sign-in fails', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
    mockSignIn.mockResolvedValue({ 
      ok: false, 
      error: 'Authentication failed', 
      status: 401, 
      url: null 
    })

    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(screen.getByText('Authentication failed. Please try again.')).toBeInTheDocument()
    })
  })

  it('redirects to dashboard on successful sign-in', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
    mockSignIn.mockResolvedValue({ ok: true, error: null, status: 200, url: null })

    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })
  })

  it('handles unexpected errors gracefully', async () => {
    const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
    mockSignIn.mockRejectedValue(new Error('Network error'))

    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument()
    })
  })

  it('has proper accessibility attributes', () => {
    render(<SignIn />)
    
    const googleButton = screen.getByRole('button', { name: /continue with google/i })
    expect(googleButton).toBeInTheDocument()
    expect(googleButton).not.toHaveAttribute('aria-disabled', 'true')
  })
})
