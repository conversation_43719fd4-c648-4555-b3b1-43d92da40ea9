import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Orders from '../page'

// Mock the StatusBadge component
jest.mock('../../../components/StatusBadge', () => ({
  StatusBadge: ({ status }: { status: string }) => <span data-testid="status-badge">{status}</span>
}))

describe('Orders Page', () => {
  it('renders the orders page correctly', () => {
    render(<Orders />)
    
    expect(screen.getByText('Pizza Orders')).toBeInTheDocument()
    expect(screen.getByText(/streamline your pizza order workflow/i)).toBeInTheDocument()
  })

  it('displays the status legend', () => {
    render(<Orders />)
    
    expect(screen.getByText('Order Status Guide')).toBeInTheDocument()
    expect(screen.getByText('Awaiting preparation')).toBeInTheDocument()
    expect(screen.getByText('Being made')).toBeInTheDocument()
    expect(screen.getByText('On the way')).toBeInTheDocument()
    expect(screen.getByText('Completed')).toBeInTheDocument()
    expect(screen.getByText('Order cancelled')).toBeInTheDocument()
  })

  it('displays filter controls', () => {
    render(<Orders />)
    
    expect(screen.getByPlaceholderText('Search orders...')).toBeInTheDocument()
    expect(screen.getByDisplayValue('All Statuses')).toBeInTheDocument()
    expect(screen.getByText('Clear Filters')).toBeInTheDocument()
  })

  it('displays the orders table', () => {
    render(<Orders />)
    
    // Check table headers
    expect(screen.getByText('Order ID')).toBeInTheDocument()
    expect(screen.getByText('Customer')).toBeInTheDocument()
    expect(screen.getByText('Pizza Type')).toBeInTheDocument()
    expect(screen.getByText('Quantity')).toBeInTheDocument()
    expect(screen.getByText('Order Date')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
  })

  it('displays order data in the table', () => {
    render(<Orders />)
    
    // Check for some sample data
    expect(screen.getByText('PZA001')).toBeInTheDocument()
    expect(screen.getByText('Arjun Sharma')).toBeInTheDocument()
    expect(screen.getByText('Margherita')).toBeInTheDocument()
  })

  it('filters orders by search term', async () => {
    const user = userEvent.setup()
    render(<Orders />)
    
    const searchInput = screen.getByPlaceholderText('Search orders...')
    await user.type(searchInput, 'Arjun')
    
    await waitFor(() => {
      expect(screen.getByText('Arjun Sharma')).toBeInTheDocument()
      // Other customers should not be visible
      expect(screen.queryByText('Priya Patel')).not.toBeInTheDocument()
    })
  })

  it('filters orders by status', async () => {
    const user = userEvent.setup()
    render(<Orders />)
    
    const statusFilter = screen.getByDisplayValue('All Statuses')
    await user.selectOptions(statusFilter, 'Delivered')
    
    await waitFor(() => {
      // Should only show delivered orders
      const statusBadges = screen.getAllByTestId('status-badge')
      statusBadges.forEach(badge => {
        expect(badge).toHaveTextContent('Delivered')
      })
    })
  })

  it('clears filters when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<Orders />)
    
    // Apply filters
    const searchInput = screen.getByPlaceholderText('Search orders...')
    await user.type(searchInput, 'Arjun')
    
    const statusFilter = screen.getByDisplayValue('All Statuses')
    await user.selectOptions(statusFilter, 'Delivered')
    
    // Clear filters
    const clearButton = screen.getByText('Clear Filters')
    await user.click(clearButton)
    
    await waitFor(() => {
      expect(searchInput).toHaveValue('')
      expect(statusFilter).toHaveValue('')
    })
  })

  it('sorts orders when column headers are clicked', async () => {
    const user = userEvent.setup()
    render(<Orders />)
    
    const orderIdHeader = screen.getByText('Order ID')
    await user.click(orderIdHeader)
    
    // Check if sorting indicator appears
    await waitFor(() => {
      expect(screen.getByText('↑')).toBeInTheDocument()
    })
  })

  it('shows empty state when no orders match filters', async () => {
    const user = userEvent.setup()
    render(<Orders />)
    
    const searchInput = screen.getByPlaceholderText('Search orders...')
    await user.type(searchInput, 'NonexistentCustomer')
    
    await waitFor(() => {
      expect(screen.getByText('🍕')).toBeInTheDocument()
      expect(screen.getByText('No orders found')).toBeInTheDocument()
      expect(screen.getByText('Try adjusting your search or filter criteria.')).toBeInTheDocument()
    })
  })

  it('displays correct order count', () => {
    render(<Orders />)
    
    expect(screen.getByText(/\(28 orders\)/)).toBeInTheDocument()
  })

  it('has responsive table design', () => {
    const { container } = render(<Orders />)
    
    expect(container.querySelector('.overflow-x-auto')).toBeInTheDocument()
    expect(container.querySelector('.min-w-full')).toBeInTheDocument()
  })
})
