{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8d69df._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ac18bf.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8YZSr0G3NQnBwg4vE4BLMLyyJlgWVFhUjue4qKESf3k=", "__NEXT_PREVIEW_MODE_ID": "1e81230a43fe906cf3b6a7670fec0d70", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5019385a43428a996dcfffd5b8899613ff103c32bf07a60b1f4ba344bba3d6e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4d148fd07dca3679e76b830097da7d3165261a211eb15ab9cdb72b22ef94a016"}}}, "instrumentation": null, "functions": {}}