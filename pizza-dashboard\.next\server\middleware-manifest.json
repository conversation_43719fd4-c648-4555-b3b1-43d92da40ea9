{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8d69df._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ac18bf.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8YZSr0G3NQnBwg4vE4BLMLyyJlgWVFhUjue4qKESf3k=", "__NEXT_PREVIEW_MODE_ID": "439bc3d95f82f0a198ce1957d34ca973", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d4bf15646df3b9495b4544a3c443ec2f451a70c1ec4410978d129ff4f06d1569", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc437fe6acf2305d55c09d5061d99b0601617e1bb74095be2415ac4bc8835e89"}}}, "sortedMiddleware": ["/"], "functions": {}}